from pydantic import BaseModel, ConfigDict, EmailStr, <PERSON>
from typing import List, Optional
from modules.user.domain.models import UserStatus

class UserBase(BaseModel):
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr

class UserCreate(UserBase):
    password: str = Field(..., min_length=8, description="Password must contain letters and numbers")

class UserPublic(UserBase):
    id: int
    status: UserStatus

class UserListResponse(BaseModel):
    users: List[UserPublic]
    total: int
    skip: int
    limit: int

class UserStatusUpdate(BaseModel):
    status: UserStatus