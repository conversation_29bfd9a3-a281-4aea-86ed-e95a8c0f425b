# Code Optimization Guide

This guide provides comprehensive tools and workflows for maintaining high code quality in the AI4SE MCP Hub project.

## 🛠️ Available Tools

### 1. **Ruff** - Modern Python Linter & Formatter
- **Purpose**: Fast linting and formatting
- **Features**: Import sorting, code formatting, error detection
- **Configuration**: `pyproject.toml` → `[tool.ruff]`

### 2. **Black** - Code Formatter
- **Purpose**: Consistent code formatting
- **Features**: Automatic code style enforcement
- **Configuration**: `pyproject.toml` → `[tool.black]`

### 3. **isort** - Import Sorting
- **Purpose**: Organize and sort imports
- **Features**: Group imports by type, remove duplicates
- **Configuration**: `pyproject.toml` → `[tool.isort]`

### 4. **autoflake** - Unused Import Removal
- **Purpose**: Clean up unused imports and variables
- **Features**: Automatic cleanup of dead code

### 5. **MyPy** - Type Checking
- **Purpose**: Static type checking
- **Features**: Catch type-related errors before runtime
- **Configuration**: `pyproject.toml` → `[tool.mypy]`

## 🚀 Quick Start

### Install Development Dependencies
```bash
# Using uv (recommended)
uv add --dev ruff black isort autoflake mypy invoke

# Or using Invoke
inv install-dev
```

### Run Complete Optimization
```bash
# Using the optimization script
python scripts/code_optimize.py

# Using Invoke (recommended)
inv optimize

# Check only (no changes)
inv optimize-check
```

## 📋 Available Commands

### Using the Optimization Script
```bash
# Full optimization
python scripts/code_optimize.py

# Check only (no changes)
python scripts/code_optimize.py --check-only

# Skip type checking
python scripts/code_optimize.py --skip-types
```

### Using Invoke (Recommended)
```bash
# Code formatting
inv format           # Format code with autoflake, isort, and ruff

# Linting
inv lint             # Lint and fix code with ruff

# Quality checks
inv check            # Check code without making changes
inv type-check       # Run type checking with mypy

# Complete workflows
inv optimize         # Run complete optimization
inv optimize-check   # Check optimization without changes

# Testing
inv test             # Run all tests
inv test-cov         # Run tests with coverage report

# Database
inv db-upgrade       # Run database migrations
inv db-downgrade     # Downgrade database

# Utility
inv clean            # Clean cache and temporary files
inv server           # Start development server
inv info             # Show project information

# Workflows
inv pre-commit       # Simulate pre-commit checks
inv ci               # Run CI checks
```

### Individual Tool Commands
```bash
# Ruff
ruff check .                    # Check for issues
ruff check --fix .              # Fix issues automatically
ruff format .                   # Format code

# Black
black .                         # Format code
black --check .                 # Check formatting

# isort
isort .                         # Sort imports
isort --check-only .            # Check import sorting

# autoflake
autoflake --remove-all-unused-imports --remove-unused-variables --in-place --recursive .

# MyPy
mypy modules/ common/ tests/    # Type checking
```

## ⚙️ Configuration

All tools are configured in `pyproject.toml`:

```toml
[tool.ruff]
line-length = 88
target-version = "py312"

[tool.ruff.lint]
select = ["E", "W", "F", "I", "B", "C4", "UP", "N", "S", "T20", "SIM"]
ignore = ["E501", "E402", "B008", "S101", "S603", "T201", "N999"]

[tool.black]
line-length = 88
target-version = ['py312']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.12"
strict = true
```

## 🔄 Recommended Workflow

### Before Committing
```bash
# 1. Run complete optimization
inv optimize

# 2. Run tests
inv test

# 3. Check everything is clean
inv check
```

### CI/CD Pipeline
```bash
# Check code quality without making changes
inv ci
```

### Pre-commit Hook
```bash
# Simulate pre-commit checks
inv pre-commit
```

## 📊 Code Quality Metrics

After running optimization, you'll see:
- ✅ **Formatting**: Consistent code style
- ✅ **Import Organization**: Clean, sorted imports
- ✅ **Linting**: No code quality issues
- ✅ **Type Safety**: Type annotations verified
- ✅ **Test Coverage**: 71% coverage maintained

## 🎯 Best Practices

1. **Run optimization before every commit**
2. **Use `--check-only` in CI/CD pipelines**
3. **Fix type errors gradually** (use `--skip-types` if needed)
4. **Maintain test coverage** above 70%
5. **Review changes** before committing optimized code

## 🔧 Troubleshooting

### Common Issues

**MyPy Type Errors**: Use `--skip-types` flag temporarily
```bash
python scripts/code_optimize.py --skip-types
```

**Import Errors**: Check if all dependencies are installed
```bash
uv add --dev ruff black isort autoflake mypy
```

**Configuration Issues**: Verify `pyproject.toml` syntax
```bash
ruff check pyproject.toml
```

## 📈 Integration with IDEs

### VS Code
Install extensions:
- Python
- Ruff
- Black Formatter
- isort

### PyCharm
Configure external tools for each formatter in Settings → Tools → External Tools.

---

**Happy Coding! 🎉**

For more information, see the individual tool documentation:
- [Ruff](https://docs.astral.sh/ruff/)
- [Black](https://black.readthedocs.io/)
- [isort](https://pycqa.github.io/isort/)
- [MyPy](https://mypy.readthedocs.io/)
