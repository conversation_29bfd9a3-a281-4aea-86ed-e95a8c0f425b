from modules.user.domain.models import User, UserStatus
from modules.user.domain.repositories import UserRepository
from typing import List, Optional

class UserService:
    def __init__(self, user_repository: UserRepository):
        self.user_repository = user_repository

    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        return await self.user_repository.get_by_id(user_id)

    async def get_user_by_username(self, username: str) -> Optional[User]:
        return await self.user_repository.get_by_username(username)

    async def get_user_by_email(self, email: str) -> Optional[User]:
        return await self.user_repository.get_by_email(email)

    async def get_user_by_username_or_email(self, username_or_email: str) -> Optional[User]:
        user = await self.get_user_by_username(username_or_email)
        if user:
            return user
        return await self.get_user_by_email(username_or_email)

    async def create_user(self, user: User) -> User:
        return await self.user_repository.save(user)

    async def update_user(self, user: User) -> User:
        return await self.user_repository.save(user)

    async def delete_user(self, user_id: int) -> bool:
        return await self.user_repository.delete(user_id)
        
    async def update_user_status(self, user_id: int, status: UserStatus) -> bool:
        return await self.user_repository.update_status(user_id, status)
    
    async def list_users(self, skip: int = 0, limit: int = 100, status: Optional[UserStatus] = None) -> List[User]:
        return await self.user_repository.list_users(skip, limit, status)
    
    async def search_users(self, query: str, skip: int = 0, limit: int = 100) -> List[User]:
        return await self.user_repository.search_users(query, skip, limit)