from typing import Optional, Dict, List
from sqlalchemy.orm import Session
from sqlalchemy import or_
from .orm import UserDB
from modules.user.domain.models import User, UserStatus
from modules.user.domain.repositories import UserRepository

# --- Data Transfer Functions ---

def to_domain(db_user: UserDB) -> User:
    """Converts ORM model to domain model."""
    return User(
        id=db_user.id,
        username=db_user.username,
        email=db_user.email,
        status=db_user.status,
        organization_id=db_user.organization_id,
        department=db_user.department,
        role=db_user.role,
        it_code=db_user.it_code,
        work_email=db_user.work_email,
        employee_id=db_user.employee_id
    )

def to_orm(user: User) -> UserDB:
    """Converts domain model to ORM model."""
    return UserDB(
        id=user.id,
        username=user.username,
        email=user.email,
        status=user.status,
        organization_id=user.organization_id,
        department=user.department,
        role=user.role,
        it_code=user.it_code,
        work_email=user.work_email,
        employee_id=user.employee_id
    )

# --- SQLAlchemy Repository Implementation ---

class UserRepositoryImpl(UserRepository):
    def __init__(self, db: Session):
        self.db = db

    async def get_by_id(self, user_id: int) -> Optional[User]:
        db_user = self.db.query(UserDB).filter(UserDB.id == user_id).first()
        return to_domain(db_user) if db_user else None

    async def get_by_username(self, username: str) -> Optional[User]:
        db_user = self.db.query(UserDB).filter(UserDB.username == username).first()
        return to_domain(db_user) if db_user else None

    async def get_by_email(self, email: str) -> Optional[User]:
        db_user = self.db.query(UserDB).filter(UserDB.email == email).first()
        return to_domain(db_user) if db_user else None

    async def save(self, user: User) -> User:
        db_user = self.db.query(UserDB).filter(UserDB.id == user.id).first()
        if db_user:
            # Update existing user
            for key, value in user.dict(exclude={'id'}).items():
                setattr(db_user, key, value)
        else:
            # Create new user
            db_user = to_orm(user)
            self.db.add(db_user)
        
        self.db.commit()
        self.db.refresh(db_user)
        return to_domain(db_user)

    async def delete(self, user_id: int) -> bool:
        db_user = self.db.query(UserDB).filter(UserDB.id == user_id).first()
        if db_user:
            self.db.delete(db_user)
            self.db.commit()
            return True
        return False

    async def update_status(self, user_id: int, status: UserStatus) -> bool:
        db_user = self.db.query(UserDB).filter(UserDB.id == user_id).first()
        if not db_user:
            return False
        db_user.status = status
        self.db.commit()
        return True

    async def list_users(
        self,
        skip: int = 0,
        limit: int = 100,
        status: UserStatus | None = None
    ) -> List[User]:
        query = self.db.query(UserDB)
        if status:
            query = query.filter(UserDB.status == status)
        users_db = query.offset(skip).limit(limit).all()
        return [to_domain(u) for u in users_db]

    async def search_users(
        self,
        query: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[User]:
        search_pattern = f"%{query}%"
        users_db = self.db.query(UserDB).filter(
            or_(
                UserDB.username.ilike(search_pattern),
                UserDB.email.ilike(search_pattern)
            )
        ).offset(skip).limit(limit).all()
        return [to_domain(u) for u in users_db]

# --- InMemory Repository Implementation (for testing/development) ---

class InMemoryUserRepository(UserRepository):
    def __init__(self):
        self._users: Dict[int, User] = {}
        self._counter = 1

    async def get_by_id(self, user_id: int) -> Optional[User]:
        return self._users.get(user_id)

    async def get_by_username(self, username: str) -> Optional[User]:
        return next(
            (user for user in self._users.values() if user.username == username),
            None
        )
        
    async def get_by_email(self, email: str) -> Optional[User]:
        return next(
            (user for user in self._users.values() if user.email == email),
            None
        )

    async def save(self, user: User) -> User:
        if user.id == 0 or user.id not in self._users:
            user.id = self._counter
            self._counter += 1
        self._users[user.id] = user
        return user

    async def delete(self, user_id: int) -> bool:
        if user_id in self._users:
            del self._users[user_id]
            return True
        return False