# 用户模块重构方案

## 解决的问题
1. 将 `user_management` 重命名为 `user`
2. 迁移 auth 模块中用户管理功能到 user 模块
3. 统一 UserRepository 实现

## 执行步骤
```mermaid
graph LR
    A[重命名模块] --> B[迁移服务功能]
    B --> C[创建用户路由]
    C --> D[更新依赖注入]
    D --> E[移除冗余代码]
```

### 详细步骤
1. **模块重命名**：
   - 将 `modules/user_management` 重命名为 `modules/user`
   - 更新所有导入路径

2. **服务层增强**：
   ```python
   # modules/user/application/services.py
   class UserService:
       async def update_user_status(self, user_id: int, status: UserStatus) -> bool:
           return await self.user_repository.update_status(user_id, status)
       
       async def list_users(self, skip=0, limit=100, status=None) -> List[User]:
           return await self.user_repository.list_users(skip, limit, status)
       
       async def search_users(self, query: str, skip=0, limit=100) -> List[User]:
           return await self.user_repository.search_users(query, skip, limit)
   ```

3. **路由迁移**：
   - 创建 `modules/user/interfaces/user_api.py`
   - 迁移用户管理路由

4. **依赖调整**：
   ```python
   # modules/auth/application/services.py
   from modules.user.application.services import UserService
   
   class AuthService:
       def __init__(self, user_service: UserService, ...):
           self.user_service = user_service
   ```

5. **清理冗余**：
   - 删除 `modules/auth/infrastructure/repositories.py` 中的 UserRepositoryImpl
   - 移除 auth 模块的用户管理路由