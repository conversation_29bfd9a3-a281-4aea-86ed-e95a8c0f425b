# AI4SE MCP Hub - Development Makefile
# 
# This Makefile provides convenient commands for code quality and optimization tasks.
# Make sure you have activated the virtual environment before running these commands.

.PHONY: help install-dev format lint check test optimize clean

# Default target
help:
	@echo "AI4SE MCP Hub - Development Commands"
	@echo "===================================="
	@echo ""
	@echo "Setup Commands:"
	@echo "  install-dev     Install development dependencies"
	@echo ""
	@echo "Code Quality Commands:"
	@echo "  format          Format code with ruff and black"
	@echo "  lint            Lint code with ruff"
	@echo "  check           Check code without making changes"
	@echo "  type-check      Run type checking with mypy"
	@echo "  optimize        Run complete code optimization"
	@echo ""
	@echo "Testing Commands:"
	@echo "  test            Run all tests"
	@echo "  test-cov        Run tests with coverage report"
	@echo ""
	@echo "Database Commands:"
	@echo "  db-upgrade      Run database migrations"
	@echo "  db-downgrade    Downgrade database by one revision"
	@echo ""
	@echo "Utility Commands:"
	@echo "  clean           Clean cache and temporary files"
	@echo ""

# Install development dependencies
install-dev:
	@echo "📦 Installing development dependencies..."
	uv add --dev ruff black isort autoflake mypy
	@echo "✅ Development dependencies installed"

# Format code
format:
	@echo "🎨 Formatting code..."
	autoflake --remove-all-unused-imports --remove-unused-variables --in-place --recursive .
	isort .
	ruff format .
	@echo "✅ Code formatting completed"

# Lint code
lint:
	@echo "🔍 Linting code..."
	ruff check --fix .
	@echo "✅ Linting completed"

# Check code without making changes
check:
	@echo "🔍 Checking code quality..."
	autoflake --remove-all-unused-imports --remove-unused-variables --recursive .
	isort --check-only --diff .
	ruff format --check .
	ruff check .
	@echo "✅ Code quality check completed"

# Type checking
type-check:
	@echo "🔍 Running type checks..."
	mypy modules/ common/ tests/
	@echo "✅ Type checking completed"

# Complete optimization
optimize:
	@echo "🚀 Running complete code optimization..."
	python scripts/code_optimize.py
	@echo "✅ Code optimization completed"

# Check only (no changes)
optimize-check:
	@echo "🔍 Checking code optimization..."
	python scripts/code_optimize.py --check-only
	@echo "✅ Code optimization check completed"

# Run tests
test:
	@echo "🧪 Running tests..."
	pytest
	@echo "✅ Tests completed"

# Run tests with coverage
test-cov:
	@echo "🧪 Running tests with coverage..."
	pytest --cov=modules --cov-report=html --cov-report=term
	@echo "✅ Tests with coverage completed"
	@echo "📊 Coverage report generated in htmlcov/"

# Database migrations
db-upgrade:
	@echo "🗄️  Running database migrations..."
	alembic upgrade head
	@echo "✅ Database migrations completed"

db-downgrade:
	@echo "🗄️  Downgrading database..."
	alembic downgrade -1
	@echo "✅ Database downgrade completed"

# Clean cache and temporary files
clean:
	@echo "🧹 Cleaning cache and temporary files..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	find . -type f -name "*.pyo" -delete 2>/dev/null || true
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	rm -rf .pytest_cache/ 2>/dev/null || true
	rm -rf .coverage 2>/dev/null || true
	rm -rf htmlcov/ 2>/dev/null || true
	rm -rf .mypy_cache/ 2>/dev/null || true
	rm -rf .ruff_cache/ 2>/dev/null || true
	@echo "✅ Cleanup completed"

# Pre-commit hook simulation
pre-commit: format lint type-check test
	@echo "✅ Pre-commit checks completed successfully"

# CI simulation
ci: check type-check test
	@echo "✅ CI checks completed successfully"
