# AI4SE MCP Hub

FastAPI 项目，使用 UV 管理依赖

## 项目结构

- `main.py`: FastAPI 主应用文件
- `requirements.txt`: 项目依赖
- `venv/`: Python 虚拟环境
- `control.bat`: Windows 服务控制脚本
- `test_main.py`: 自动化测试文件

## 快速开始

1. 激活虚拟环境:
   ```cmd
   .\venv\Scripts\activate
   ```

2. 安装依赖:
   ```cmd
   uv pip install -r requirements.txt
   ```

3. 启动服务:
   ```cmd
   uvicorn main:app --reload
   ```

## 服务控制

### Windows CMD 使用:
使用 control.bat 脚本管理服务:

- 启动服务:
  ```cmd
  control.bat start
  ```

- 停止服务:
  ```cmd
  control.bat stop
  ```

- 重启服务:
  ```cmd
  control.bat restart
  ```

### Git Bash 使用:
使用 control.sh 脚本管理服务:

- 启动服务:
  ```bash
  ./control.sh start
  ```

- 停止服务:
  ```bash
  ./control.sh stop
  ```

- 重启服务:
  ```bash
  ./control.sh restart
  ```

## 运行测试

```cmd
pytest -v