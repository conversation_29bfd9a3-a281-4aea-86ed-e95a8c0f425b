from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
import jwt
from common.dependencies import get_db, get_config, AppConfig  # 导入AppConfig
from modules.auth.application.services import AuthService
# 修正导入路径：UserRepository 和 UserRepositoryImpl 属于 user 模块
from modules.user.application.services import UserService
from modules.user.domain.repositories import UserRepository
from modules.user.infrastructure.repositories import UserRepositoryImpl
from modules.auth.domain.repositories import OAuthAccountRepository
from modules.auth.infrastructure.repositories import OAuthAccountRepositoryImpl
from modules.user.domain.models import User

# 定义Auth模块专属依赖
def get_user_repository(db: Session = Depends(get_db)) -> UserRepository:
    return UserRepositoryImpl(db)

def get_oauth_account_repository(db: Session = Depends(get_db)) -> OAuthAccountRepository:
    return OAuthAccountRepositoryImpl(db)

def get_user_service(user_repo: UserRepository = Depends(get_user_repository)) -> UserService:
    return UserService(user_repo)

def get_auth_service(
    user_service: UserService = Depends(get_user_service),
    oauth_account_repo: OAuthAccountRepository = Depends(get_oauth_account_repository),
    config: AppConfig = Depends(get_config)
) -> AuthService:
    return AuthService(
        user_service=user_service,
        oauth_account_repo=oauth_account_repo,
        secret_key=config.jwt_secret,
        algorithm=config.jwt_algorithm,
        token_expire_minutes=config.token_expire_minutes
    )

# JWT token verification
security = HTTPBearer()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    user_service: UserService = Depends(get_user_service),
    config: AppConfig = Depends(get_config)
) -> User:
    """Verify JWT token and return current user"""
    try:
        payload = jwt.decode(
            credentials.credentials,
            config.jwt_secret,
            algorithms=[config.jwt_algorithm]
        )
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except jwt.PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    user = await user_service.get_user_by_username(username)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return user