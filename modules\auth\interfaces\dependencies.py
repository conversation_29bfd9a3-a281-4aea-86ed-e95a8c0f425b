from fastapi import Depends
from sqlalchemy.orm import Session
from common.dependencies import get_db, get_config, AppConfig  # 导入AppConfig
from modules.auth.application.services import AuthService
# 修正导入路径：UserRepository 和 UserRepositoryImpl 属于 user 模块
from modules.user.application.services import UserService
from modules.user.domain.repositories import UserRepository
from modules.user.infrastructure.repositories import UserRepositoryImpl
from modules.auth.domain.repositories import OAuthAccountRepository
from modules.auth.infrastructure.repositories import OAuthAccountRepositoryImpl

# 定义Auth模块专属依赖
def get_user_repository(db: Session = Depends(get_db)) -> UserRepository:
    return UserRepositoryImpl(db)

def get_oauth_account_repository(db: Session = Depends(get_db)) -> OAuthAccountRepository:
    return OAuthAccountRepositoryImpl(db)

def get_user_service(user_repo: UserRepository = Depends(get_user_repository)) -> UserService:
    return UserService(user_repo)

def get_auth_service(
    user_service: UserService = Depends(get_user_service),
    oauth_account_repo: OAuthAccountRepository = Depends(get_oauth_account_repository),
    config: AppConfig = Depends(get_config)
) -> AuthService:
    return AuthService(
        user_service=user_service,
        oauth_account_repo=oauth_account_repo,
        secret_key=config.jwt_secret,
        algorithm=config.jwt_algorithm,
        token_expire_minutes=config.token_expire_minutes
    )