# Invoke Tasks - Python Task Runner

This project uses **Invoke** as a cross-platform Python task runner, replacing traditional Makefiles for better Windows compatibility.

## 🚀 Quick Start

### Install Invoke
```bash
# Invoke is already included in dev dependencies
uv add --dev invoke

# Or install manually
pip install invoke
```

### View Available Tasks
```bash
# List all available tasks
inv --list

# Show detailed help
inv help
```

## 📋 Available Tasks

### Code Quality
```bash
inv format           # Format code with autoflake, isort, and ruff
inv lint             # Lint and fix code with ruff
inv check            # Check code without making changes
inv type-check       # Run type checking with mypy
inv optimize         # Run complete code optimization
inv optimize-check   # Check optimization without changes
```

### Testing
```bash
inv test             # Run all tests
inv test-cov         # Run tests with coverage report
```

### Database
```bash
inv db-upgrade       # Run database migrations
inv db-downgrade     # Downgrade database by one revision
```

### Development
```bash
inv server           # Start development server
inv clean            # Clean cache and temporary files
inv info             # Show project information
```

### Dependencies
```bash
inv install-dev      # Install development dependencies
inv deps-update      # Update all dependencies
inv deps-audit       # Audit dependencies for security issues
```

### Workflows
```bash
inv pre-commit       # Simulate pre-commit checks (format + lint + type-check + test)
inv ci               # Run CI checks (check + type-check + test)
```

## 🔧 Task Parameters

Some tasks accept parameters:

```bash
# Start server with custom host and port
inv server --host=0.0.0.0 --port=8080

# Start server without auto-reload
inv server --no-reload
```

## 🎯 Recommended Workflow

### Before Committing
```bash
# 1. Run complete optimization
inv optimize

# 2. Run tests
inv test

# 3. Check everything is clean
inv check
```

### Or use the combined workflow
```bash
# Run all pre-commit checks
inv pre-commit
```

### CI/CD Pipeline
```bash
# Check code quality without making changes
inv ci
```

## 📁 Task Configuration

Tasks are defined in `tasks.py` in the project root. You can:

1. **View task source**: Open `tasks.py` to see how tasks work
2. **Add custom tasks**: Add new `@task` decorated functions
3. **Modify existing tasks**: Edit task implementations as needed

## 🆚 Comparison with Make

| Feature | Make | Invoke |
|---------|------|--------|
| Cross-platform | ❌ (Windows issues) | ✅ |
| Python integration | ❌ | ✅ |
| Parameter support | Limited | ✅ |
| Task dependencies | ✅ | ✅ |
| Conditional logic | Limited | ✅ (Python) |
| IDE support | Limited | ✅ |

## 🔍 Troubleshooting

### Task Not Found
```bash
# Make sure you're in the project root
cd /path/to/ai4se-mcp-hub

# Check if tasks.py exists
ls tasks.py

# List available tasks
inv --list
```

### Import Errors
```bash
# Make sure virtual environment is activated
.\.venv\Scripts\activate  # Windows
source .venv/bin/activate  # Linux/macOS

# Install dependencies
inv install-dev
```

### Unicode Errors
All Unicode characters have been replaced with ASCII equivalents to ensure compatibility across different terminal encodings.

## 📚 Learn More

- [Invoke Documentation](https://docs.pyinvoke.org/)
- [Task Configuration Guide](./CODE_OPTIMIZATION_GUIDE.md)
- [Project Setup Guide](./README.md)

---

**Happy Coding! 🎉**

Run `inv help` for detailed information about all available tasks.
