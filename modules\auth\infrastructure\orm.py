from sqlalchemy import Column, String, DateTime, Enum, UUID, func, ForeignKey
from sqlalchemy.orm import declarative_base, relationship
from modules.user.domain.models import UserStatus
from modules.auth.domain.models import OAuthProvider
import uuid

Base = declarative_base()

class UserORM(Base):
    __tablename__ = 'users'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    status = Column(Enum(UserStatus), nullable=False, default=UserStatus.ACTIVE)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

class OAuthAccountORM(Base):
    __tablename__ = 'oauth_accounts'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    provider = Column(Enum(OAuthProvider), nullable=False)
    provider_account_id = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=func.now())
    
    user = relationship("UserORM", back_populates="oauth_accounts")

UserORM.oauth_accounts = relationship("OAuthAccountORM", order_by=OAuthAccountORM.id, back_populates="user")