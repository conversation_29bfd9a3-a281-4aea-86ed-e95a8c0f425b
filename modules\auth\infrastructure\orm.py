from sqlalchemy import Column, String, DateTime, Enum, Integer, func, ForeignKey
from sqlalchemy.orm import relationship
from common.db.database import Base
from modules.auth.domain.models import OAuthProvider
import uuid

class OAuthAccountORM(Base):
    __tablename__ = 'oauth_accounts'

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    provider = Column(Enum(OAuthProvider), nullable=False)
    provider_account_id = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=func.now())

    # Note: relationship will be defined when needed