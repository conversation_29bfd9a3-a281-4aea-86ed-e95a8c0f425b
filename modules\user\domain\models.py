from pydantic import BaseModel, EmailStr, ConfigDict, Field
from enum import Enum

class UserStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"

class User(BaseModel):
    """
    Domain Entity: User
    This is the core domain model, containing only user state and identity information,
    without any infrastructure logic (such as cryptography).
    """
    model_config = ConfigDict(from_attributes=True)

    id: int
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr
    hashed_password: str | None = None
    status: UserStatus = Field(default=UserStatus.ACTIVE, description="User status")