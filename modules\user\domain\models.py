from pydantic import BaseModel, EmailStr, ConfigDict, Field
from enum import Enum

class UserStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"

class User(BaseModel):
    """
    Domain Entity: User
    This is the core domain model, containing only user state and identity information,
    without any infrastructure logic (such as cryptography).
    """
    model_config = ConfigDict(from_attributes=True)

    id: int
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr
    hashed_password: str | None = None
    status: UserStatus = Field(default=UserStatus.ACTIVE, description="User status")
    organization_id: int | None = None
    department: str | None = None
    role: str | None = None
    it_code: str | None = None
    work_email: EmailStr | None = None
    employee_id: str | None = None
    oauth_provider: str | None = None
    oauth_id: str | None = None