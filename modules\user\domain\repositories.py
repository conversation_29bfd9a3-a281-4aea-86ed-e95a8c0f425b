from abc import ABC, abstractmethod
from typing import Optional
from .models import User  # Updated import to use local model

class UserRepository(ABC):
    @abstractmethod
    async def get_by_id(self, user_id: int) -> Optional[User]:
        """Gets a user by their ID."""
        raise NotImplementedError

    @abstractmethod
    async def get_by_username(self, username: str) -> Optional[User]:
        """Gets a user by their username."""
        raise NotImplementedError

    @abstractmethod
    async def get_by_email(self, email: str) -> Optional[User]:
        """Gets a user by their email."""
        raise NotImplementedError

    @abstractmethod
    async def save(self, user: User) -> User:
        """Saves a user (creates or updates)."""
        raise NotImplementedError

    @abstractmethod
    async def delete(self, user_id: int) -> bool:
        """Deletes a user by their ID."""
        raise NotImplementedError