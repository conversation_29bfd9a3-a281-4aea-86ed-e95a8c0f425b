from abc import ABC, abstractmethod
from modules.auth.domain.models import OAuthAccount
from typing import Optional, List

class OAuthAccountRepository(ABC):
    @abstractmethod
    async def get_by_provider_and_id(self, provider: str, provider_id: str) -> Optional[OAuthAccount]:
        pass

    @abstractmethod
    async def create(self, oauth_account: OAuthAccount) -> OAuthAccount:
        pass

    @abstractmethod
    async def get_by_user_id(self, user_id: str) -> List[OAuthAccount]:
        pass