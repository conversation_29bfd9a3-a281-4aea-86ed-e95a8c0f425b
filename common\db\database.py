import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base

# --- Database URL ---
# In a real app, use env variables and a proper config system
DATABASE_URL = os.getenv("AI4SE_MCP_HUB_DB_URL", "sqlite:///./ai4se-mcp-hub.db")

# --- SQLAlchemy Engine ---
connect_args = {}
if DATABASE_URL.startswith("sqlite"):
    connect_args = {"check_same_thread": False}

engine = create_engine(DATABASE_URL, connect_args=connect_args)

# --- SQLAlchemy Session ---
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# --- Base for ORM models ---
Base = declarative_base()