from fastapi import APIRouter, Depends, HTTPException
from modules.user.application.services import UserService
from modules.user.domain.models import UserStatus
from .schemas import UserPublic, UserStatusUpdate
from .dependencies import get_user_service
from typing import List, Optional

router = APIRouter()

@router.get("/users/", response_model=List[UserPublic])
async def list_users(
    skip: int = 0,
    limit: int = 100,
    status: Optional[UserStatus] = None,
    user_service: UserService = Depends(get_user_service)
):
    return await user_service.list_users(skip, limit, status)

@router.get("/users/search", response_model=List[UserPublic])
async def search_users(
    query: str,
    skip: int = 0,
    limit: int = 100,
    user_service: UserService = Depends(get_user_service)
):
    return await user_service.search_users(query, skip, limit)

@router.patch("/users/{user_id}/status", response_model=bool)
async def update_user_status(
    user_id: int,
    status_update: UserStatusUpdate,
    user_service: UserService = Depends(get_user_service)
):
    return await user_service.update_user_status(user_id, status_update.status)