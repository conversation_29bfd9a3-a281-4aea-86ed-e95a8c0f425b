from typing import List, Optional
from modules.auth.domain.repositories import OAuthAccountRepository
from modules.auth.domain.models import OAuthAccount, OAuthProvider
from modules.auth.infrastructure.orm import OAuthAccountORM
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from uuid import UUID

class OAuthAccountRepositoryImpl(OAuthAccountRepository):
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_by_provider_and_id(self, provider: str, provider_id: str) -> Optional[OAuthAccount]:
        stmt = select(OAuthAccountORM).where(
            OAuthAccountORM.provider == OAuthProvider(provider),
            OAuthAccountORM.provider_account_id == provider_id
        )
        result = await self.session.execute(stmt)
        oauth_orm = result.scalar_one_or_none()
        if oauth_orm:
            return OAuthAccount(
                id=oauth_orm.id,
                user_id=oauth_orm.user_id,
                provider=oauth_orm.provider,
                provider_account_id=oauth_orm.provider_account_id,
                created_at=oauth_orm.created_at
            )
        return None

    async def create(self, oauth_account: OAuthAccount) -> OAuthAccount:
        oauth_orm = OAuthAccountORM(
            user_id=oauth_account.user_id,
            provider=oauth_account.provider,
            provider_account_id=oauth_account.provider_account_id
        )
        self.session.add(oauth_orm)
        await self.session.flush()
        await self.session.refresh(oauth_orm)
        return OAuthAccount(
            id=oauth_orm.id,
            user_id=oauth_orm.user_id,
            provider=oauth_orm.provider,
            provider_account_id=oauth_orm.provider_account_id,
            created_at=oauth_orm.created_at
        )

    async def get_by_user_id(self, user_id: str) -> List[OAuthAccount]:
        try:
            user_uuid = UUID(user_id)
        except (ValueError, TypeError):
            return []

        stmt = select(OAuthAccountORM).where(OAuthAccountORM.user_id == user_uuid)
        result = await self.session.execute(stmt)
        oauth_accounts_orm = result.scalars().all()
        return [
            OAuthAccount(
                id=orm.id,
                user_id=orm.user_id,
                provider=orm.provider,
                provider_account_id=orm.provider_account_id,
                created_at=orm.created_at
            )
            for orm in oauth_accounts_orm
        ]