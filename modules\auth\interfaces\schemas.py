from pydantic import BaseModel, EmailStr, ConfigDict, <PERSON>
from typing import Optional
from uuid import UUID
from datetime import datetime
from modules.auth.domain.models import OAuthProvider
from modules.user.domain.models import UserStatus

# ====================
# User Schemas
# ====================

class UserBase(BaseModel):
    username: str
    email: EmailStr

class UserCreate(UserBase):
    """Schema for creating a new user."""
    password: str = Field(...,
        min_length=8,
        description="密码需包含大小写字母和数字",
        json_schema_extra={"example": "Passw0rd!"})

class UserPublic(UserBase):
    """Schema for public user data (e.g., in responses)."""
    id: int
    status: UserStatus

class UserStatusUpdate(BaseModel):
    """Schema for updating user status."""
    status: UserStatus

class UserListResponse(BaseModel):
    """Response schema for paginated user lists."""
    users: list[UserPublic]
    total: int
    skip: int
    limit: int

# ====================
# Token Schemas
# ====================

class Token(BaseModel):
    """Schema for the access token response."""
    access_token: str = Field(..., json_schema_extra={"example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."})
    token_type: str = Field(..., json_schema_extra={"example": "bearer"})

class TokenData(BaseModel):
    """Schema for the data encoded in the JWT."""
    username: str | None = None
    id: int | None = None

# ====================
# OAuth Account Schema
# ====================

class OAuthAccountSchema(BaseModel):
    """Schema for OAuth account data."""
    id: UUID
    user_id: UUID
    provider: OAuthProvider
    provider_account_id: str
    created_at: datetime

class OAuthCallbackRequest(BaseModel):
    """Schema for OAuth callback request."""
    provider: str
    oauth_id: str
    email: EmailStr
    name: Optional[str] = None