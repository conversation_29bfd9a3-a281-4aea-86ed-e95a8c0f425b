from sqlalchemy import Column, Integer, String, Enum
from common.db.database import Base
from modules.user.domain.models import UserStatus

class UserDB(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    status = Column(Enum(UserStatus), default=UserStatus.ACTIVE, nullable=False)
    
    organization_id = Column(Integer, nullable=True)
    department = Column(String, nullable=True)
    role = Column(String, nullable=True)
    it_code = Column(String, nullable=True)
    work_email = Column(String, nullable=True)
    employee_id = Column(String, nullable=True)