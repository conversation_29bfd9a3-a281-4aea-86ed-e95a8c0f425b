# 认证模块重构方案

## 解决的问题
1. 移除用户管理功能
2. 精简服务层职责
3. 解决代码重复问题

## 执行步骤
```mermaid
graph LR
    A[移除用户管理功能] --> B[精简服务层]
    B --> C[更新依赖关系]
    C --> D[清理冗余代码]
```

### 详细步骤
1. **服务层精简**：
   ```python
   # modules/auth/application/services.py
   class AuthService:
       # 保留核心认证功能
       async def register_user(self, ...): ...
       async def authenticate_user(self, ...): ...
       async def handle_oauth_callback(self, ...): ...
       # 移除用户管理方法
   ```

2. **路由清理**：
   - 删除 `modules/auth/interfaces/user_api.py`
   - 移除用户管理相关路由

3. **依赖更新**：
   ```python
   # modules/auth/application/services.py
   from modules.user.application.services import UserService
   
   class AuthService:
       def __init__(
           self,
           user_service: UserService,  # 使用user模块的服务
           oauth_account_repo: OAuthAccountRepository,
           ...
       ):
           self.user_service = user_service
   ```

4. **仓储层清理**：
   - 删除 `modules/auth/infrastructure/repositories.py` 中的 UserRepositoryImpl
   - 仅保留 OAuthAccountRepositoryImpl

5. **测试调整**：
   - 将用户管理测试迁移到 user 模块
   - 更新 auth 模块测试用例