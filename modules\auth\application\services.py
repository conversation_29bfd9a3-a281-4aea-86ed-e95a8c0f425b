from datetime import datetime, timedelta, timezone
from typing import Op<PERSON>, <PERSON><PERSON>
import jwt
from passlib.context import Crypt<PERSON>ontext
from pydantic import EmailStr
from modules.user.application.services import UserService
from modules.user.domain.models import User, UserStatus
from modules.auth.domain.models import OAuth<PERSON><PERSON>ider, OAuthAccount
from modules.auth.domain.repositories import OAuthAccountRepository

class AuthService:
    def __init__(
        self,
        user_service: UserService,
        oauth_account_repo: OAuthAccountRepository,
        secret_key: str,
        algorithm: str = "HS256",
        token_expire_minutes: int = 30,
    ):
        self.user_service = user_service
        self.oauth_account_repo = oauth_account_repo
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.token_expire_minutes = token_expire_minutes
        # Configure passlib to suppress bcrypt version warnings
        self.pwd_context = CryptContext(
            schemes=["bcrypt"],
            deprecated="auto",
            bcrypt__default_rounds=12
        )

    def _verify_password(self, plain_password: str, hashed_password: str) -> bool:
        return self.pwd_context.verify(plain_password, hashed_password)

    def _get_password_hash(self, password: str) -> str:
        return self.pwd_context.hash(password)

    async def register_user(self, username: str, email: EmailStr, password: str) -> User:
        existing_user = await self.user_service.get_user_by_username(username)
        if existing_user:
            raise ValueError("Username already exists")

        hashed_password = self._get_password_hash(password)
        # Create User domain object
        new_user = User(
            username=username,
            email=email,
            hashed_password=hashed_password,
            status=UserStatus.ACTIVE
        )
        return await self.user_service.create_user(new_user)

    async def authenticate_user(self, username_or_email: str, password: str) -> Optional[User]:
        user = await self.user_service.get_user_by_username_or_email(username_or_email)
        if not user or not self._verify_password(password, user.hashed_password):
            return None
        return user

    def create_access_token(self, user: User) -> str:
        expire = datetime.now(timezone.utc) + timedelta(minutes=self.token_expire_minutes)
        to_encode = {"sub": user.username, "id": user.id, "exp": expire}
        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
    
    async def handle_oauth_callback(
        self,
        provider: str,
        oauth_id: str,
        email: str,
        name: Optional[str] = None,
        **kwargs
    ) -> Tuple[User, str]:
        """Handles OAuth callback and returns user with access token."""
        # First check if we already have an OAuth account for this provider+id
        oauth_account = await self.oauth_account_repo.get_by_provider_and_id(provider, oauth_id)
        if oauth_account:
            # If OAuth account exists, get the associated user
            user = await self.user_service.get_user_by_id(oauth_account.user_id)
        else:
            # Find user by email
            user = await self.user_service.get_user_by_email(email)
            
            # Create user if not exists
            if not user:
                username = email.split('@')[0]
                new_user = User(
                    username=username,
                    email=email,
                    status=UserStatus.ACTIVE
                )
                user = await self.user_service.create_user(new_user)
            
            # Create new OAuth account
            new_oauth = OAuthAccount(
                user_id=user.id,
                provider=OAuthProvider(provider),
                provider_account_id=oauth_id
            )
            await self.oauth_account_repo.create(new_oauth)
        
        # Generate access token
        access_token = self.create_access_token(user)
        return user, access_token
